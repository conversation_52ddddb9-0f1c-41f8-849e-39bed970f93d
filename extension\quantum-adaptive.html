<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KOS Quantum Edge Bot</title>
    <link rel="stylesheet" href="quantum-adaptive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="quantum-container">
        <!-- Quantum Status Panel -->
        <div class="quantum-status-panel">
            <div class="quantum-status-header">
                <i class="fas fa-atom quantum-icon"></i>
                <span class="quantum-status-text">Quantum Field Initialized</span>
            </div>
            <div class="quantum-metrics">
                <div class="metric">
                    <span class="metric-label">Coherence</span>
                    <span class="metric-value" id="coherenceValue">87%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Entanglement</span>
                    <span class="metric-value" id="entanglementValue">92%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Probability</span>
                    <span class="metric-value" id="probabilityValue">78%</span>
                </div>
            </div>
        </div>

        <!-- Trading Controls -->
        <div class="trading-controls">
            <button id="activateBtn" class="quantum-btn primary">
                <i class="fas fa-play"></i>
                <span>Start Signal Generation</span>
            </button>
            <button id="pauseBtn" class="quantum-btn secondary" disabled>
                <i class="fas fa-pause"></i>
                <span>Pause</span>
            </button>
        </div>

        <!-- Signal Display Panel -->
        <div class="signal-panel" id="signalPanel" style="display: none;">
            <div class="signal-header">
                <i class="fas fa-satellite-dish signal-icon"></i>
                <span class="signal-title">Quantum Signal Generated</span>
            </div>
            <div class="signal-content">
                <div class="signal-direction" id="signalDirection">CALL</div>
                <div class="signal-details">
                    <div class="signal-item">
                        <span class="signal-label">Recommended Amount:</span>
                        <span class="signal-value" id="signalAmount">$0.00</span>
                    </div>
                    <div class="signal-item">
                        <span class="signal-label">Confidence:</span>
                        <span class="signal-value" id="signalConfidence">85%</span>
                    </div>
                    <div class="signal-item">
                        <span class="signal-label">Expiry:</span>
                        <span class="signal-value" id="signalExpiry">5m</span>
                    </div>
                </div>
            </div>

            <!-- Manual Result Input -->
            <div class="result-input">
                <div class="result-header">Enter Trade Result:</div>
                <div class="result-buttons">
                    <button id="winBtn" class="result-btn win-btn">
                        <i class="fas fa-check"></i>
                        <span>WIN</span>
                    </button>
                    <button id="lossBtn" class="result-btn loss-btn">
                        <i class="fas fa-times"></i>
                        <span>LOSS</span>
                    </button>
                </div>
                <div class="result-note">Execute the trade manually on Pocket Option, then click the result<br>
                    <small>Keyboard shortcuts: W = Win, L = Loss</small>
                </div>
            </div>
        </div>

        <!-- Configuration Panel -->
        <div class="config-panel">
            <div class="config-group">
                <label class="config-label">Risk Capital</label>
                <div class="config-input-group">
                    <input type="number" id="riskCapital" class="config-input" value="50" min="1" max="100">
                    <span class="config-unit">%</span>
                </div>
                <div class="config-suggestion">Percentage of balance to risk</div>
            </div>

            <div class="config-group">
                <label class="config-label">Profit Target</label>
                <div class="config-input-group">
                    <input type="number" id="profitTarget" class="config-input" value="10" min="1" max="100">
                    <span class="config-unit">%</span>
                </div>
                <div class="config-suggestion">Target profit percentage</div>
            </div>

            <div class="config-group">
                <label class="config-label">Quantum Multiplier</label>
                <div class="config-input-group">
                    <input type="number" id="quantumMultiplier" class="config-input" value="2.087" min="1.1" max="5" step="0.001">
                </div>
                <div class="config-suggestion">Adaptive progression factor</div>
            </div>

            <div class="config-group">
                <label class="config-label">Max Sequence</label>
                <div class="config-input-group">
                    <input type="number" id="maxSequence" class="config-input" value="5" min="3" max="10">
                    <span class="config-unit">trades</span>
                </div>
                <div class="config-suggestion">Maximum consecutive trades</div>
            </div>
        </div>

        <!-- Trading Status -->
        <div class="trading-status">
            <div class="status-header">
                <i class="fas fa-chart-line"></i>
                <span>Signal Status</span>
            </div>
            <div class="status-content">
                <div class="status-item">
                    <span class="status-label">Current State:</span>
                    <span class="status-value" id="currentState">Signal generator ready</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Next Action:</span>
                    <span class="status-value" id="nextAction">Awaiting activation</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Signals Generated:</span>
                    <span class="status-value" id="signalsGenerated">0</span>
                </div>
            </div>
        </div>

        <!-- Statistics Panel -->
        <div class="stats-panel">
            <div class="stats-header">
                <i class="fas fa-chart-bar"></i>
                <span>Session Statistics</span>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-value" id="totalTrades">0</span>
                    <span class="stat-label">Total Trades</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="winRate">0%</span>
                    <span class="stat-label">Win Rate</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="currentProfit">$0.00</span>
                    <span class="stat-label">Current Profit</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="currentBalance">$0.00</span>
                    <span class="stat-label">Balance</span>
                </div>
            </div>
        </div>

        <!-- Trades History -->
        <div class="trades-history">
            <div class="trades-header">
                <i class="fas fa-history"></i>
                <span>Recent Trades</span>
            </div>
            <div class="trades-list" id="tradesList">
                <div class="no-trades">No trades executed yet</div>
            </div>
        </div>
    </div>

    <script src="quantum-adaptive.js"></script>
</body>
</html>
